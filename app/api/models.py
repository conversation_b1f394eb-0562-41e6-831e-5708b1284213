"""
API请求和响应模型
"""
from typing import Optional, Dict, Any, List, Literal, Union
from pydantic import BaseModel, Field, validator, model_validator
from datetime import datetime

from app.config import settings


class SearchOptions(BaseModel):
    """联网搜索选项"""
    enableInternetSearch: bool = Field(default=False, description="是否开启联网搜索")
    forceSearch: bool = Field(default=False, description="是否强制搜索（跳过智能判断）")
    provider: Union[str, List[str]] = Field(default="hiagent", description="搜索引擎提供商，支持字符串或数组")
    snippet: bool = Field(default=False, description="是否不开启内容读取，false为开启内容读取")
    authInfos: Optional[Dict[str, Dict[str, Any]]] = Field(default=None, description="搜索引擎鉴权信息")

    @validator('provider')
    def validate_provider(cls, v):
        valid_providers = ['hiagent', 'wiki', 'huatech']
        if isinstance(v, str):
            if v not in valid_providers:
                raise ValueError(f"无效的搜索提供商: {v}，支持的提供商: {', '.join(valid_providers)}")
        elif isinstance(v, list):
            for provider in v:
                if provider not in valid_providers:
                    raise ValueError(f"无效的搜索提供商: {provider}，支持的提供商: {', '.join(valid_providers)}")
        else:
            raise ValueError("provider必须是字符串或字符串数组")
        return v


class ChatRequest(BaseModel):
    """通用问答请求模型"""
    messages: List[Dict[str, str]] = Field(..., description="聊天消息历史，包含role和content；最后一条消息应为当前问题")
    model: Optional[str] = Field(default=None, description="指定的LLM模型")
    stream: bool = Field(default=False, description="是否使用流式响应")
    searchOptions: Optional[SearchOptions] = Field(default=None, description="联网搜索选项")

    @validator('messages')
    def validate_messages(cls, v):
        if not isinstance(v, list):
            raise ValueError("messages必须是数组")
        if len(v) == 0:
            raise ValueError("messages不能为空")
        for msg in v:
            if not isinstance(msg, dict):
                raise ValueError("messages中的每个元素必须是字典")
            if 'role' not in msg or 'content' not in msg:
                raise ValueError("messages中的每个元素必须包含role和content字段")
        return v

    @validator('model')
    def validate_model(cls, v):
        if v is not None and not settings.validate_model(v):
            raise ValueError(f"不支持的模型: {v}，支持的模型: {', '.join(settings.available_models)}")
        return v


class ContentSummaryRequest(BaseModel):
    """内容处理请求模型"""
    content: Optional[Union[str, List[str]]] = Field(default=None, description="待总结的内容：支持字符串或字符串数组。如果提供，将忽略messages")
    messages: Optional[List[Dict[str, str]]] = Field(default=None, description="聊天消息历史，包含role和content；在未提供content时使用")
    model: Optional[str] = Field(default=None, description="指定的LLM模型")
    stream: bool = Field(default=False, description="是否使用流式响应")
    concise_mode: bool = Field(default=False, description="是否启用精炼模式（更简洁、通俗易懂的总结）")
    history_strategy: Literal["truncate", "summarize"] = Field(
        default="truncate",
        description="历史压缩策略：truncate直接截取最近消息；summarize使用大模型摘要",
    )

    @validator('content')
    def validate_content(cls, v):
        if v is None:
            return v
        if isinstance(v, str):
            if not v.strip():
                raise ValueError("content字符串不能为空")
            return v
        if isinstance(v, list):
            if len(v) == 0:
                raise ValueError("content数组不能为空")
            for item in v:
                if not isinstance(item, str) or not item.strip():
                    raise ValueError("content数组中的每个元素必须是非空字符串")
            return v
        raise ValueError("content必须是字符串或字符串数组")

    @model_validator(mode="after")
    def check_content_or_messages(self):
        content = self.content
        messages = self.messages
        is_content_missing = False
        if content is None:
            is_content_missing = True
        elif isinstance(content, str):
            is_content_missing = (content.strip() == "")
        elif isinstance(content, list):
            is_content_missing = (len(content) == 0)
        else:
            is_content_missing = True

        if is_content_missing and (not messages or len(messages) == 0):
            raise ValueError("content与messages至少需要提供一个")
        return self

    @validator('messages')
    def validate_messages(cls, v):
        if v is None:
            return v
        if not isinstance(v, list):
            raise ValueError("messages必须是数组")
        for msg in v:
            if not isinstance(msg, dict):
                raise ValueError("messages中的每个元素必须是字典")
            if 'role' not in msg or 'content' not in msg:
                raise ValueError("messages中的每个元素必须包含role和content字段")
        return v

    @validator('model')
    def validate_model(cls, v):
        if v is not None and not settings.validate_model(v):
            raise ValueError(f"不支持的模型: {v}，支持的模型: {', '.join(settings.available_models)}")
        return v


# ContentSummaryResponse removed - using raw LLM API response passthrough


class ImageParseRequest(BaseModel):
    """图片解析请求模型"""
    url: str = Field(..., description="图片URL地址")
    question: str = Field(default="描述这个图片", description="对图片的问题")
    stream: bool = Field(default=False, description="是否使用流式响应")

    @validator('url')
    def validate_url(cls, v):
        if not v or not v.strip():
            raise ValueError("图片URL不能为空")
        # 简单的URL格式验证
        if not (v.startswith('http://') or v.startswith('https://')):
            raise ValueError("图片URL必须以http://或https://开头")
        return v.strip()


class TranslateOptions(BaseModel):
    """翻译选项"""
    src_lang: Optional[str] = Field(default=None, description="源语言代码，如果不提供则自动检测")
    tgt_lang: str = Field(default="zh", description="目标语言代码")
    provider: str = Field(default="doubao", description="翻译服务提供商")

    @validator('provider')
    def validate_provider(cls, v):
        valid_providers = ['doubao', 'llm_translate']
        if v not in valid_providers:
            raise ValueError(f"不支持的翻译提供商: {v}，支持的提供商: {', '.join(valid_providers)}")
        return v

class TranslationRequest(BaseModel):
    """翻译请求模型"""
    question: Union[str, List[Dict[str, str]]] = Field(..., description="待翻译的文本，支持字符串或数组格式。字符串格式：直接传入文本；数组格式：每个元素包含sourceText字段")
    messages: Optional[List[Dict[str, str]]] = Field(default=None, description="历史会话记录，包含role和content；用于提供翻译上下文")
    stream: bool = Field(default=False, description="是否使用流式响应")
    translateOptions: Optional[TranslateOptions] = Field(default=None, description="翻译选项")
    model: Optional[str] = Field(default=None, description="指定的LLM模型（仅在provider为llm_translate时生效）")

    @validator('question')
    def validate_question(cls, v):
        # 支持字符串格式
        if isinstance(v, str):
            if not v.strip():
                raise ValueError("question字符串不能为空")
            return v

        # 支持数组格式（原有逻辑）
        if isinstance(v, list):
            if len(v) == 0:
                raise ValueError("question数组不能为空")
            for item in v:
                if not isinstance(item, dict):
                    raise ValueError("question数组中的每个元素必须是字典")
                if 'sourceText' not in item:
                    raise ValueError("question数组中的每个元素必须包含sourceText字段")
                if not isinstance(item['sourceText'], str) or not item['sourceText'].strip():
                    raise ValueError("sourceText字段不能为空")
            return v

        # 不支持的类型
        raise ValueError("question必须是字符串或数组格式")

    @validator('messages')
    def validate_messages(cls, v):
        if v is None:
            return v
        if not isinstance(v, list):
            raise ValueError("messages必须是数组")
        if len(v) == 0:
            return v  # 允许空数组
        for msg in v:
            if not isinstance(msg, dict):
                raise ValueError("messages中的每个元素必须是字典")
            if 'role' not in msg or 'content' not in msg:
                raise ValueError("messages中的每个元素必须包含role和content字段")
        return v


class PromptOptimizerRequest(BaseModel):
    """提示词优化请求模型"""
    question: str = Field(..., description="需要优化的原始提示词")
    stream: bool = Field(default=False, description="是否使用流式响应")
    model: Optional[str] = Field(default=None, description="指定的LLM模型")

    @validator('question')
    def validate_question(cls, v):
        if not isinstance(v, str):
            raise ValueError("question必须是字符串")
        if not v.strip():
            raise ValueError("question不能为空")
        return v.strip()

    @validator('model')
    def validate_model(cls, v):
        if v is not None and not settings.validate_model(v):
            raise ValueError(f"不支持的模型: {v}，支持的模型: {', '.join(settings.available_models)}")
        return v


class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""
    status: str
    version: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    uptime: Optional[float] = None
    services: Optional[Dict[str, str]] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "version": "1.0.0",
                "timestamp": "2024-01-01T00:00:00Z",
                "uptime": 3600.0,
                "services": {
                    "llm_service": "healthy",
                    "database": "healthy"
                }
            }
        }


class ModelListResponse(BaseModel):
    """模型列表响应模型"""
    models: list[str]
    default_model: str
    api_url: str
    
    class Config:
        json_schema_extra = {
            "example": {
                "models": [
                    "ht::saas-deepseek-v3",
                    "ht::saas-deepseek-r1",
                    "ht::saas-doubao-15-pro-32k"
                ],
                "default_model": "ht::saas-deepseek-v3",
                "api_url": "http://168.63.85.222/web/unauth/LLM_api_proxy/v1/chat/completions"
            }
        }
