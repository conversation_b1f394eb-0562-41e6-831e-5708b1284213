"""
Translation agent with Lang<PERSON>raph workflow - supports multiple translation providers
"""
from typing import Op<PERSON>, AsyncGenerator, Union, Dict, Any, List
import json

from langgraph.graph import StateGraph, END

from app.agents.base import BaseAgent, AgentWorkflowState
from app.core.models import AgentType, TaskStatus
from app.services.translation_service import translation_service, TranslationRequest, TranslationProvider
from app.utils.logger import get_logger
from app.config import settings
from app.agents.content.context_utils import (
    estimate_messages_tokens,
)

logger = get_logger(__name__)


class TranslationAgent(BaseAgent):
    """Translation agent with multi-provider support"""

    def __init__(self, agent_type: AgentType, **kwargs):
        """Initialize Translation Agent

        Args:
            agent_type: The agent type
            **kwargs: Additional keyword arguments (ignored for compatibility with agent registry)
        """
        super().__init__(
            agent_type=agent_type,
            name="Translation Agent",
            description="Multi-provider translation agent with intelligent API selection"
        )

    def _build_graph(self):
        """Build LangGraph workflow for translation"""
        workflow = StateGraph(AgentWorkflowState)

        # Add processing nodes
        workflow.add_node("validate_input", self._validate_input_node)
        workflow.add_node("compact_history", self._compact_history_node)
        workflow.add_node("select_provider", self._select_provider_node)
        workflow.add_node("translate_text", self._translate_text_node)
        workflow.add_node("validate_result", self._validate_result_node)
        workflow.add_node("fallback_translate", self._fallback_translate_node)
        workflow.add_node("finalize", self._finalize_node)

        # Define workflow edges
        workflow.set_entry_point("validate_input")

        # Route based on whether messages are provided
        workflow.add_conditional_edges(
            "validate_input",
            self._route_after_validation,
            {
                "compact_history": "compact_history",
                "select_provider": "select_provider",
            },
        )
        workflow.add_edge("compact_history", "select_provider")
        workflow.add_edge("select_provider", "translate_text")
        
        # Conditional routing after translation
        workflow.add_conditional_edges(
            "translate_text",
            self._route_after_translation,
            {
                "success": "finalize",
                "retry": "fallback_translate",
                "failed": "finalize"
            }
        )
        workflow.add_edge("fallback_translate", "finalize")
        workflow.add_edge("validate_result", "finalize")
        workflow.add_edge("finalize", END)

        # Compile the graph
        self.graph = workflow.compile()
        logger.info("LangGraph workflow built for translation agent")

    async def _validate_input_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Validate translation input - supports both string and array formats"""
        try:
            state["current_step"] = "validate_input"
            input_data = state["input_data"]

            # Validate required fields
            if "question" not in input_data or not input_data["question"]:
                raise ValueError("question field is required and cannot be empty")

            question = input_data["question"]

            # Validate question format (string or array)
            if isinstance(question, str):
                # String format validation
                if not question.strip():
                    raise ValueError("question string cannot be empty")
                logger.info("检测到字符串格式输入")
            elif isinstance(question, list):
                # Array format validation (original logic)
                if len(question) == 0:
                    raise ValueError("question array cannot be empty")
                for item in question:
                    if not isinstance(item, dict) or "sourceText" not in item:
                        raise ValueError("Each question item must be a dict with 'sourceText' field")
                    if not isinstance(item['sourceText'], str) or not item['sourceText'].strip():
                        raise ValueError("sourceText field cannot be empty")
                logger.info("检测到数组格式输入")
            else:
                raise ValueError("question must be either a string or an array of objects")

            # 检测输入格式但不转换
            is_string_input = isinstance(question, str)

            # Set defaults
            translate_options = input_data.get("translateOptions", {})
            # 确保有默认的 provider
            if "provider" not in translate_options:
                translate_options["provider"] = "llm_translate"

            model_param = input_data.get("model")
            messages = input_data.get("messages", [])

            # Validate messages if provided
            if messages:
                if not isinstance(messages, list):
                    raise ValueError("messages must be a list")
                for msg in messages:
                    if not isinstance(msg, dict) or 'role' not in msg or 'content' not in msg:
                        raise ValueError("Each message must be a dict with 'role' and 'content' fields")

            # Store validated input (保持原始格式)
            state["step_results"]["validated_input"] = {
                "question": question,  # 保持原始格式
                "is_string_input": is_string_input,  # 输入格式标记
                "stream": input_data.get("stream", False),
                "translate_options": translate_options,
                "model": model_param,
                "messages": messages,  # 添加历史会话
                "has_messages": bool(messages)  # 标记是否有历史会话
            }

            logger.info(f"finish _validate_input_node is_string_input:{is_string_input},question:{question}")
            logger.info(f"完整input_data: {input_data}")

        except Exception as e:
            state["error"] = f"Input validation failed: {str(e)}"
            logger.error(f"Input validation failed: {e}")

        return state

    def _route_after_validation(self, state: AgentWorkflowState) -> str:
        """Route after input validation based on whether messages are provided"""
        step_results = state["step_results"]
        validated_input = step_results.get("validated_input", {})

        if validated_input.get("has_messages", False):
            logger.info("检测到历史会话，进入简化历史处理节点")
            return "compact_history"
        else:
            logger.info("无历史会话，直接进入提供商选择节点")
            return "select_provider"

    async def _compact_history_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Filter and process history messages for translation context"""
        try:
            state["current_step"] = "compact_history"
            step_results = state["step_results"]
            validated_input = step_results["validated_input"]

            messages = validated_input.get("messages", [])
            model = validated_input.get("model")

            if not messages:
                # 如果没有历史会话，直接跳过
                state["step_results"]["processed_messages"] = []
                logger.info("无历史会话需要处理")
                return state

            # 使用翻译专用的历史消息处理策略
            processed_messages = self._filter_translation_history(messages, model)
            token_count = estimate_messages_tokens(processed_messages)

            state["step_results"]["processed_messages"] = processed_messages
            state["step_results"]["prompt_tokens"] = token_count

            logger.info(
                f"翻译历史会话处理完成 - 过滤到 {len(processed_messages)} 条消息，tokens≈{token_count}"
            )

        except Exception as e:
            state["error"] = f"History processing failed: {str(e)}"
            logger.error(f"History processing failed: {e}")
            # 失败时设置空的处理结果，继续流程
            state["step_results"]["processed_messages"] = []

        return state

    def _filter_translation_history(self, messages: List[Dict[str, str]], model: Optional[str]) -> List[Dict[str, str]]:
        """
        翻译专用的历史消息过滤策略

        核心原则：
        1. 优先保留最新用户消息，确保当前翻译意图不丢失
        2. 保留翻译相关的上下文信息（术语定义、专业领域等）
        3. 实现token预算控制，避免超出模型限制
        4. 简单截断而不是摘要，避免信息失真
        """
        if not messages:
            return []

        # 翻译相关关键词（扩展版）
        translation_keywords = [
            # 基础翻译词汇
            "翻译", "translate", "translation", "翻译为", "翻译成", "translate to", "translate into",
            # 术语和专业词汇
            "术语", "专业", "领域", "terminology", "term", "专业术语", "行业术语",
            # 语言相关
            "语言", "language", "英文", "中文", "日文", "韩文", "法文", "德文", "西班牙文", "俄文",
            "english", "chinese", "japanese", "korean", "french", "german", "spanish", "russian",
            # 专业领域
            "技术", "商务", "学术", "医学", "法律", "金融", "经济", "科技", "工程",
            "technical", "business", "academic", "medical", "legal", "financial", "economic",
            # 翻译质量和要求
            "准确", "精确", "一致性", "上下文", "语境", "风格", "正式", "非正式",
            "accuracy", "precise", "consistency", "context", "style", "formal", "informal"
        ]

        # 计算上下文预算
        context_window = settings.get_context_window(model)
        max_tokens = settings.default_max_tokens
        safety_margin = 600  # 翻译任务给更多安全边界，考虑到可能的长文本
        allowed_input_tokens = max(1000, context_window - max_tokens - safety_margin)

        # 找到最新的用户消息索引
        last_user_idx = None
        for i in range(len(messages) - 1, -1, -1):
            if messages[i].get("role") == "user":
                last_user_idx = i
                break

        # 策略1: 确保最新用户消息一定被保留
        must_keep_messages = []
        if last_user_idx is not None:
            must_keep_messages = [messages[last_user_idx]]
            logger.debug(f"标记最新用户消息必须保留: {messages[last_user_idx].get('content', '')[:50]}...")

        # 策略2: 从后往前逐步添加消息，直到达到预算限制
        selected_messages = []
        current_tokens = 0

        # 从最新消息开始向前遍历
        for i in range(len(messages) - 1, -1, -1):
            msg = messages[i]
            msg_tokens = estimate_messages_tokens([msg])

            # 检查添加这条消息是否会超出预算
            if current_tokens + msg_tokens <= allowed_input_tokens:
                selected_messages.insert(0, msg)  # 插入到开头保持顺序
                current_tokens += msg_tokens
            else:
                # 如果是必须保留的消息（最新用户消息），强制保留
                if msg in must_keep_messages:
                    # 移除一些较早的消息为必须保留的消息腾出空间
                    while current_tokens + msg_tokens > allowed_input_tokens and len(selected_messages) > 0:
                        removed = selected_messages.pop(0)
                        current_tokens -= estimate_messages_tokens([removed])
                        logger.debug(f"为保留最新用户消息，移除较早消息: {removed.get('content', '')[:30]}...")

                    selected_messages.insert(0, msg)
                    current_tokens += msg_tokens
                else:
                    # 检查是否为翻译相关消息，如果是则尝试保留
                    content = msg.get("content", "").lower()
                    is_translation_related = any(keyword in content for keyword in translation_keywords)

                    if is_translation_related and len(selected_messages) > 2:
                        # 尝试移除一些不太重要的消息来为翻译相关消息腾出空间
                        removed_tokens = 0
                        messages_to_remove = []

                        for j, selected_msg in enumerate(selected_messages):
                            if selected_msg not in must_keep_messages:
                                selected_content = selected_msg.get("content", "").lower()
                                selected_is_translation_related = any(kw in selected_content for kw in translation_keywords)

                                if not selected_is_translation_related:
                                    messages_to_remove.append(j)
                                    removed_tokens += estimate_messages_tokens([selected_msg])
                                    if removed_tokens >= msg_tokens:
                                        break

                        # 执行移除
                        for j in reversed(messages_to_remove):
                            removed = selected_messages.pop(j)
                            current_tokens -= estimate_messages_tokens([removed])
                            logger.debug(f"为翻译相关消息腾出空间，移除: {removed.get('content', '')[:30]}...")

                        # 如果腾出了足够空间，添加翻译相关消息
                        if current_tokens + msg_tokens <= allowed_input_tokens:
                            selected_messages.insert(0, msg)
                            current_tokens += msg_tokens
                            logger.debug(f"保留翻译相关消息: {msg.get('content', '')[:30]}...")

        logger.info(f"翻译历史消息处理完成 - 原始: {len(messages)} 条，保留: {len(selected_messages)} 条，tokens≈{current_tokens}")

        # 验证最新用户消息是否被保留
        if last_user_idx is not None:
            last_user_msg = messages[last_user_idx]
            if last_user_msg not in selected_messages:
                logger.warning("最新用户消息未被保留，这可能影响翻译质量")

        return selected_messages

    async def _select_provider_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Select translation provider"""
        try:
            state["current_step"] = "select_provider"
            step_results = state["step_results"]
            validated_input = step_results["validated_input"]

            # Get provider from translate_options
            translate_options = validated_input.get("translate_options", {})
            provider = translate_options.get("provider", "doubao")

            # Validate provider
            if provider not in [TranslationProvider.DOUBAO, TranslationProvider.LLM_TRANSLATE]:
                logger.warning(f"Unknown provider: {provider}, using default doubao")
                provider = TranslationProvider.DOUBAO

            state["step_results"]["selected_provider"] = provider
            logger.info(f"选择翻译提供商: {provider}")

        except Exception as e:
            state["error"] = f"Provider selection failed: {str(e)}"
            logger.error(f"Provider selection failed: {e}")

        return state

    async def _translate_text_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Execute translation"""
        try:
            state["current_step"] = "translate_text"
            step_results = state["step_results"]
            validated_input = step_results["validated_input"]

            # Build translation request - 直接使用原始的translate_options
            original_translate_options = validated_input["translate_options"]
            translate_options = {
                "src_lang": original_translate_options.get("src_lang"),  # 可能为None，让下游处理
                "tgt_lang": original_translate_options.get("tgt_lang", "zh"),
                "provider": step_results["selected_provider"]
            }
            model_param = validated_input.get("model")
            processed_messages = step_results.get("processed_messages", [])

            translation_request = TranslationRequest(
                question=validated_input["question"],
                messages=processed_messages,  # 传递处理后的历史会话
                stream=validated_input["stream"],
                translate_options=translate_options,
                model=model_param,
                _is_string_input=validated_input.get("is_string_input", False)
            )

            provider = step_results["selected_provider"]
            is_stream = translation_request.stream
            logger.info(f"开始翻译: provider={provider}, stream={is_stream}, model={model_param}")
            logger.info(f"TranslationRequest.model: {translation_request.model}")

            # 根据提供商类型和流式参数选择处理方式

            if is_stream:
                # 流式模式：支持所有提供商的流式输出
                logger.info(f"provider-{provider}, stream ------- the translation_request is {translation_request}")
                stream_generator = translation_service.stream_translate(translation_request)
                state["step_results"]["translation_stream"] = stream_generator
                # 流式模式下不设置translation_result，而是设置stream标志
                state["step_results"]["is_streaming"] = True

            else:
                # 非流式模式：按照现有逻辑处理（同步返回完整结果）
                logger.info(f"provider-{provider}, non-stream")
                result = await translation_service.translate(translation_request)
                state["step_results"]["translation_result"] = result

            state["step_results"]["attempt_count"] = state["step_results"].get("attempt_count", 0) + 1

            if state["step_results"].get("is_streaming"):
                logger.info("流式翻译已准备就绪")
            else:
                result = state["step_results"].get("translation_result")
                logger.info(f"翻译完成: code={result.code if result else 'unknown'}")

        except Exception as e:
            state["error"] = f"Translation failed: {str(e)}"
            logger.error(f"Translation failed: {e}")

        return state

    def _route_after_translation(self, state: AgentWorkflowState) -> str:
        """Route after translation based on result"""
        step_results = state["step_results"]

        # 检查是否为流式翻译
        if step_results.get("is_streaming"):
            logger.info("流式翻译模式，直接进入finalize")
            return "success"

        # 非流式翻译的原有逻辑
        result = step_results.get("translation_result")
        attempt_count = step_results.get("attempt_count", 0)

        if not result or result.code != "success":
            if attempt_count < 2:  # Max 1 retry
                logger.info("翻译失败，尝试回退策略")
                return "retry"
            else:
                logger.warning("翻译失败，已达到最大重试次数")
                return "failed"

        return "success"

    async def _validate_result_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Validate translation result"""
        try:
            state["current_step"] = "validate_result"
            # Additional result validation logic can be added here
            logger.info("翻译结果验证完成")

        except Exception as e:
            state["error"] = f"Result validation failed: {str(e)}"
            logger.error(f"Result validation failed: {e}")

        return state

    async def _fallback_translate_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Fallback translation strategy"""
        try:
            state["current_step"] = "fallback_translate"
            step_results = state["step_results"]
            validated_input = step_results["validated_input"]
            current_provider = step_results["selected_provider"]

            # Switch to alternative provider
            fallback_provider = (TranslationProvider.LLM_TRANSLATE
                                if current_provider == TranslationProvider.DOUBAO
                                else TranslationProvider.DOUBAO)

            logger.info(f"使用回退翻译策略: {current_provider} -> {fallback_provider}")

            # Build fallback translation request - 直接使用原始的translate_options
            original_translate_options = validated_input["translate_options"]
            translate_options = {
                "src_lang": original_translate_options.get("src_lang"),  # 可能为None，让下游处理
                "tgt_lang": original_translate_options.get("tgt_lang", "zh"),
                "provider": fallback_provider
            }
            translation_request = TranslationRequest(
                question=validated_input["question"],
                stream=validated_input["stream"],
                translate_options=translate_options,
                model=validated_input.get("model"),
                _is_string_input=validated_input.get("is_string_input", False)
            )

            # 根据回退提供商类型和流式参数选择处理方式
            provider = fallback_provider
            is_stream = translation_request.stream

            if provider == TranslationProvider.DOUBAO:
                # Doubao 提供商：非流式处理
                logger.info("回退到Doubao提供商，执行非流式翻译")
                result = await translation_service.translate(translation_request)
                state["step_results"]["translation_result"] = result

            elif provider == TranslationProvider.LLM_TRANSLATE and not is_stream:
                # LLM 提供商 + 非流式模式
                logger.info("回退到LLM提供商，执行非流式翻译")
                result = await translation_service.translate(translation_request)
                state["step_results"]["translation_result"] = result

            elif provider == TranslationProvider.LLM_TRANSLATE and is_stream:
                # LLM 提供商 + 流式模式
                logger.info("回退到LLM提供商，执行流式翻译")
                stream_generator = translation_service.stream_translate(translation_request)
                state["step_results"]["translation_stream"] = stream_generator
                state["step_results"]["is_streaming"] = True

            else:
                # 默认处理
                logger.warning(f"未知的回退提供商配置: {provider}, stream={is_stream}")
                result = await translation_service.translate(translation_request)
                state["step_results"]["translation_result"] = result

            state["step_results"]["used_fallback"] = True

            if state["step_results"].get("is_streaming"):
                logger.info("回退流式翻译已准备就绪")
            else:
                result = state["step_results"].get("translation_result")
                logger.info(f"回退翻译完成: code={result.code if result else 'unknown'}")

        except Exception as e:
            state["error"] = f"Fallback translation failed: {str(e)}"
            logger.error(f"Fallback translation failed: {e}")

        return state

    async def _finalize_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Finalize translation result"""
        try:
            state["current_step"] = "finalize"
            step_results = state["step_results"]

            # 检查是否为流式翻译
            if step_results.get("is_streaming"):
                # 流式翻译模式：将stream generator存储到final_output中
                stream_generator = step_results.get("translation_stream")
                if stream_generator:
                    state["final_output"] = stream_generator
                    logger.info("流式翻译流程完成，stream generator已准备就绪")
                else:
                    state["final_output"] = {
                        "code": "error",
                        "message": "Stream generator not found",
                        "data": []
                    }
                    logger.error("流式翻译失败：未找到stream generator")
            else:
                # 非流式翻译的原有逻辑
                result = step_results.get("translation_result")
                if result:
                    state["final_output"] = {
                        "code": result.code,
                        "message": result.message,
                        "data": result.data
                    }
                else:
                    state["final_output"] = {
                        "code": "error",
                        "message": "Translation failed",
                        "data": []
                    }

                logger.info("非流式翻译流程完成")

        except Exception as e:
            state["error"] = f"Finalization failed: {str(e)}"
            logger.error(f"Finalization failed: {e}")

        return state

    # Required abstract method implementation
    async def process_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Process node logic (required by base class)"""
        return state

    # Direct access methods for API usage
    async def process_translation(self,
                                question: Union[str, List[Dict[str, str]]],
                                messages: Optional[List[Dict[str, str]]] = None,
                                stream: bool = False,
                                translate_options: Optional[Dict[str, str]] = None,
                                model: Optional[str] = None,
                                provider: str = "doubao") -> Union[Dict[str, Any], AsyncGenerator[str, None]]:
        """Translation processing with direct result passthrough"""

        # Prepare input data for LangGraph workflow
        translate_opts = translate_options or {}
        # 确保 provider 在 translate_options 中
        if "provider" not in translate_opts:
            translate_opts["provider"] = provider

        input_data = {
            "question": question,
            "messages": messages,  # 添加历史会话支持
            "stream": stream,
            "translateOptions": translate_opts,
            "model": model  # 将 model 作为顶级字段传递
        }

        # Execute the LangGraph workflow
        agent_state = await self.execute(input_data)

        if agent_state.status == TaskStatus.FAILED:
            # Return error in appropriate format
            error_response = {
                "code": "error",
                "message": agent_state.error_message or "Translation failed",
                "data": []
            }

            if stream:
                # Stream error response
                async def error_stream():
                    yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"
                    yield "data: [DONE]\n\n"
                return error_stream()
            else:
                return error_response

        # Return successful result
        output_data = agent_state.output_data

        # 检查是否为流式翻译且有stream generator
        if stream and hasattr(output_data, '__aiter__'):
            # 流式翻译：直接返回stream generator
            logger.info("返回流式翻译生成器")
            return output_data
        elif stream:
            # 非流式结果的流式包装（用于Doubao等不支持流式的提供商）
            logger.info("返回非流式结果的流式包装")
            async def success_stream():
                yield f"data: {json.dumps(output_data, ensure_ascii=False)}\n\n"
                yield "data: [DONE]\n\n"
            return success_stream()
        else:
            # 非流式返回
            logger.info("返回非流式翻译结果")
            return output_data
